"use client"

import { useState } from "react"
import { Activity, Bot, BrainCircuit, Database, GitBranch, LineChart, Settings, Users } from "lucide-react"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { AgentActivityPanel } from "./agent-activity-panel"
import { DependencyGraph } from "./dependency-graph"
import { AnalyticsDashboard } from "./analytics-dashboard"
import { useToast } from "@/components/ui/use-toast"

export function AgentSidebar() {
  const [activeTab, setActiveTab] = useState<string>("activity")
  const { toast } = useToast()
  const [agentStatus, setAgentStatus] = useState<"active" | "paused">("paused")

  const toggleAgentStatus = () => {
    const newStatus = agentStatus === "active" ? "paused" : "active"
    setAgentStatus(newStatus)
    toast({
      title: `AI Agents ${newStatus === "active" ? "Activated" : "Paused"}`,
      description: `AI agents are now ${newStatus === "active" ? "actively" : "no longer"} monitoring and updating the board.`,
      variant: newStatus === "active" ? "default" : "destructive",
    })
  }

  return (
    <SidebarProvider defaultOpen={true}>
      <Sidebar variant="floating" side="right">
        <SidebarHeader className="flex flex-col gap-2 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BrainCircuit className="h-5 w-5 text-primary" />
              <h2 className="text-lg font-semibold">AI Agent Hub</h2>
            </div>
            <SidebarTrigger />
          </div>
          <Button
            variant={agentStatus === "active" ? "default" : "outline"}
            className={`w-full ${agentStatus === "active" ? "bg-primary" : "border-dashed"}`}
            onClick={toggleAgentStatus}
          >
            <Bot className="mr-2 h-4 w-4" />
            {agentStatus === "active" ? "Agents Active" : "Activate Agents"}
          </Button>
        </SidebarHeader>

        <SidebarContent>
          <Tabs defaultValue="activity" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid grid-cols-3 mx-4">
              <TabsTrigger value="activity">
                <Activity className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Activity</span>
              </TabsTrigger>
              <TabsTrigger value="dependencies">
                <GitBranch className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Dependencies</span>
              </TabsTrigger>
              <TabsTrigger value="analytics">
                <LineChart className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Analytics</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="activity" className="mt-2">
              <AgentActivityPanel />
            </TabsContent>

            <TabsContent value="dependencies" className="mt-2">
              <DependencyGraph />
            </TabsContent>

            <TabsContent value="analytics" className="mt-2">
              <AnalyticsDashboard />
            </TabsContent>
          </Tabs>

          <Separator className="my-4" />

          <SidebarGroup>
            <SidebarGroupLabel>Agent Management</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <button className="w-full">
                      <Users className="h-4 w-4" />
                      <span>Agent Assignments</span>
                    </button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <button className="w-full">
                      <Database className="h-4 w-4" />
                      <span>Resource Allocation</span>
                    </button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
                <SidebarMenuItem>
                  <SidebarMenuButton asChild>
                    <button className="w-full">
                      <Settings className="h-4 w-4" />
                      <span>Agent Settings</span>
                    </button>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter className="p-4">
          <div className="text-xs text-muted-foreground">
            <p>AI Agent System v1.0</p>
            <p>Last sync: {new Date().toLocaleTimeString()}</p>
          </div>
        </SidebarFooter>
      </Sidebar>
    </SidebarProvider>
  )
}
