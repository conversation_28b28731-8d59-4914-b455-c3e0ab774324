"use client"

import { memo } from "react"
import { Edit } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import type { CardType } from "./kanban-board"

interface KanbanLegendProps {
  cardTypes: CardType[]
  onEdit: () => void
}

// Use React.memo to prevent unnecessary re-renders
export const KanbanLegend = memo(function KanbanLegend({ cardTypes, onEdit }: KanbanLegendProps) {
  return (
    <div className="flex items-center justify-end gap-4 text-sm">
      <div className="font-medium">Key:</div>
      {cardTypes.map((type) => (
        <div key={type.id} className="flex items-center gap-2">
          <div className="w-4 h-4 rounded-sm" style={{ backgroundColor: type.color }}></div>
          <span>{type.name}</span>
        </div>
      ))}
      <Button variant="ghost" size="sm" onClick={onEdit} className="ml-2" type="button">
        <Edit className="h-4 w-4 mr-1" />
        Edit
      </Button>
    </div>
  )
})
