"use client"
import { Card } from "@/components/ui/card"
import type { ResourceMetrics } from "./board-context"
import { Bar<PERSON>hart, Bar, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, Legend } from "recharts"

interface ResourceUsageChartProps {
  metrics: ResourceMetrics
  mini?: boolean
  className?: string
}

export function ResourceUsageChart({ metrics, mini = false, className }: ResourceUsageChartProps) {
  const data = [
    {
      name: "Token Usage",
      value: metrics.tokenUsage,
      fill: "#8884d8",
    },
    {
      name: "CPU Time",
      value: metrics.cpuTime,
      fill: "#82ca9d",
    },
    {
      name: "Memory",
      value: metrics.memoryUsage,
      fill: "#ffc658",
    },
  ]

  // For mini chart, just show a simple visualization
  if (mini) {
    return (
      <div className={`flex h-2 w-full gap-0.5 ${className}`}>
        {data.map((item) =>
          item.value > 0 ? (
            <div
              key={item.name}
              className="h-full rounded-sm"
              style={{
                backgroundColor: item.fill,
                width: `${Math.max(5, (item.value / Math.max(...data.map((d) => d.value))) * 100)}%`,
              }}
              title={`${item.name}: ${item.value}`}
            />
          ) : null,
        )}
      </div>
    )
  }

  // For full chart
  return (
    <Card className={`p-4 ${className}`}>
      <h3 className="text-sm font-medium mb-4">Resource Usage</h3>
      <ResponsiveContainer width="100%" height={200}>
        <BarChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip formatter={(value) => [`${value}`, ""]} labelFormatter={(name) => `${name}`} />
          <Legend />
          <Bar dataKey="value" name="Usage" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    </Card>
  )
}
