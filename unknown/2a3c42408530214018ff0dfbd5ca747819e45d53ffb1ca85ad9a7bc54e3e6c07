"use client"

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Switch } from "@/components/ui/switch"
import { useTheme } from "next-themes"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { useState, useEffect } from "react"

interface SettingsDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
}

export function SettingsDialog({ open, onOpenChange }: SettingsDialogProps) {
  const { theme, setTheme } = useTheme()
  const { toast } = useToast()
  const [currentTheme, setCurrentTheme] = useState<string>("system")

  // Update the current theme state when the dialog opens
  useEffect(() => {
    if (open) {
      setCurrentTheme(theme || "system")
    }
  }, [open, theme])

  const handleSaveChanges = () => {
    // Apply theme change
    setTheme(currentTheme)

    // Also directly manipulate the DOM for immediate feedback
    if (currentTheme === "dark") {
      document.documentElement.classList.add("dark")
    } else if (currentTheme === "light") {
      document.documentElement.classList.remove("dark")
    } else {
      // System preference
      const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
      if (prefersDark) {
        document.documentElement.classList.add("dark")
      } else {
        document.documentElement.classList.remove("dark")
      }
    }

    // Save theme preference to localStorage
    localStorage.setItem("theme", currentTheme)

    toast({
      title: "Settings saved",
      description: "Your settings have been saved successfully.",
    })
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Settings</DialogTitle>
          <DialogDescription>Customize your application settings and preferences.</DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="appearance">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>

          <TabsContent value="appearance" className="space-y-4 py-4">
            <div className="grid gap-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="theme" className="text-right">
                  Theme
                </Label>
                <Select value={currentTheme} onValueChange={setCurrentTheme} className="col-span-3">
                  <SelectTrigger id="theme">
                    <SelectValue placeholder="Select theme" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="light">Light</SelectItem>
                    <SelectItem value="dark">Dark</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="animations" className="text-right">
                  Animations
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch id="animations" defaultChecked />
                  <Label htmlFor="animations" className="text-sm">
                    Enable animations
                  </Label>
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="notifications" className="space-y-4 py-4">
            <div className="grid gap-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="card-notifications" className="text-right">
                  Card Updates
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch id="card-notifications" defaultChecked />
                  <Label htmlFor="card-notifications" className="text-sm">
                    Notify on card updates
                  </Label>
                </div>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="board-notifications" className="text-right">
                  Board Changes
                </Label>
                <div className="flex items-center space-x-2 col-span-3">
                  <Switch id="board-notifications" defaultChecked />
                  <Label htmlFor="board-notifications" className="text-sm">
                    Notify on board changes
                  </Label>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button type="submit" onClick={handleSaveChanges}>
            Save changes
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
