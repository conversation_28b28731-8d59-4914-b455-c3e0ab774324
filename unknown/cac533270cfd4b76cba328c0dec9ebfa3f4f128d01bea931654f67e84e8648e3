"use client"

import { BoardSwitcher } from "@/components/board-switcher"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { CreateBoardDialog } from "@/components/create-board-dialog"
import { UserProfileDialog } from "@/components/user-profile-dialog"
import { SettingsDialog } from "@/components/settings-dialog"
import { useSearch } from "@/components/search-provider"
import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Cpu, Search, X } from "lucide-react"
import { ModeToggle } from "@/components/mode-toggle"

interface HeaderProps {
  showAgentPanel: boolean
  toggleAgentPanel: () => void
}

export function Header({ showAgentPanel, toggleAgentPanel }: HeaderProps) {
  const { searchQuery, setSearchQuery } = useSearch()
  const [isSearching, setIsSearching] = useState(false)

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex h-14 items-center px-4">
        <BoardSwitcher />
        <div className="ml-auto flex items-center space-x-2">
          {isSearching ? (
            <div className="flex items-center">
              <Input
                type="search"
                placeholder="Search..."
                className="w-64 mr-2"
                value={searchQuery}
                onChange={(e) => {
                  console.log("Search input changed:", e.target.value)
                  setSearchQuery(e.target.value)
                }}
                onKeyDown={(e) => {
                  if (e.key === "Escape") {
                    setSearchQuery("")
                    setIsSearching(false)
                  }
                }}
                autoFocus
              />
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setSearchQuery("")
                  setIsSearching(false)
                }}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <Button variant="ghost" size="icon" onClick={() => setIsSearching(true)}>
              <Search className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant={showAgentPanel ? "secondary" : "ghost"}
            size="icon"
            onClick={toggleAgentPanel}
            title={showAgentPanel ? "Hide Agent Panel" : "Show Agent Panel"}
          >
            <Cpu className="h-4 w-4" />
          </Button>
          <CreateBoardDialog />
          <SettingsDialog />
          <UserProfileDialog />
          <ModeToggle />
        </div>
      </div>
    </header>
  )
}
