import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { BoardProvider } from "@/components/board-context"
import { AgentBoardControllerProvider } from "@/components/agent-board-controller"
import { Toaster } from "@/components/ui/toaster"
import { SearchProvider } from "@/components/search-provider"
import { Suspense } from "react"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Kanban Board with AI Agent Integration",
  description: "A Kanban board with AI agent integration for task management",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider attribute="class" defaultTheme="system" enableSystem storageKey="kanban-theme">
          <SearchProvider>
            <BoardProvider>
              <AgentBoardControllerProvider>
                <Suspense>{children}</Suspense>
                <Toaster />
              </AgentBoardControllerProvider>
            </BoardProvider>
          </SearchProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
