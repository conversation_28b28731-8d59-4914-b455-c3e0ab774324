"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useBoard } from "@/components/board-context"
import { KanbanBoard } from "@/components/kanban-board"
import { Header } from "@/components/header"
import { BoardSwitcher } from "@/components/board-switcher"
import { Toaster } from "@/components/ui/toaster"
import { SearchProvider } from "@/components/search-context"

export default function NewBoardPage({ params }: { params: { boardId: string } }) {
  const { boardId } = params
  const { setActiveBoard } = useBoard()
  const router = useRouter()

  // Set the active board when the page loads
  useEffect(() => {
    setActiveBoard(boardId)
  }, [boardId, setActiveBoard])

  return (
    <SearchProvider>
      <div className="flex flex-col min-h-screen bg-[#f5f5f5] dark:bg-[#1e1e1e]">
        <Header />
        <div className="border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="container flex items-center h-10">
            <BoardSwitcher
              onBoardChange={(id) => {
                if (id !== boardId) {
                  router.push(`/?board=${id}`)
                }
              }}
            />
          </div>
        </div>
        <main className="flex-1 container mx-auto p-4 md:p-6">
          <KanbanBoard boardId={boardId} />
        </main>
        <Toaster />
      </div>
    </SearchProvider>
  )
}
