"use client"

import type React from "react"

import { createContext } from "react"

type Toast = {
  id: string
  title?: string
  description?: string
  action?: React.ReactNode
  duration?: number
}

type ToastOptions = Omit<Toast, "id">

type ToasterContextProps = {
  toasts: Toast[]
  toast: (options?: ToastOptions) => void
  dismiss: (id: string) => void
  update: (id: string, options: ToastOptions) => void
}

const ToasterContext = createContext<ToasterContextProps>({
  toasts: [],
  toast: () => {},
  dismiss: () => {},
  update: () => {},
})

import { useToast as useToastOriginal } from "@/components/ui/toast"

// Re-export the hook
export const useToast = useToastOriginal

// Export a direct toast function for convenience
export const toast = (props) => {
  // This is incorrect as it calls a hook outside a component
  // const { toast } = useToastOriginal()
  // return toast(props)

  // Instead, just re-export the hook
  console.warn("Please use the useToast() hook instead of direct toast function")
  return null
}
