"use client"

import { useState, useEffect } from "react"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

interface EditSwimlaneDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onUpdateSwimlane: (id: string, title: string) => void
  swimlane: {
    id: string
    title: string
  } | null
}

export function EditSwimlaneDialog({ open, onOpenChange, onUpdateSwimlane, swimlane }: EditSwimlaneDialogProps) {
  const [swimlaneTitle, setSwimlanelTitle] = useState("")

  // Update the title when the swimlane prop changes
  useEffect(() => {
    if (swimlane) {
      setSwimlanelTitle(swimlane.title)
    }
  }, [swimlane])

  const handleUpdateSwimlane = () => {
    if (swimlaneTitle.trim() && swimlane) {
      onUpdateSwimlane(swimlane.id, swimlaneTitle)
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit swimlane</DialogTitle>
          <DialogDescription>Update the swimlane title.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="swimlane-title" className="text-right">
              Title
            </Label>
            <Input
              id="swimlane-title"
              value={swimlaneTitle}
              onChange={(e) => setSwimlanelTitle(e.target.value)}
              className="col-span-3"
              placeholder="e.g., Frontend Tasks"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleUpdateSwimlane} disabled={!swimlaneTitle.trim()}>
            Update Swimlane
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
