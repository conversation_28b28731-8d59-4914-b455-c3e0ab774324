"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON><PERSON>nt, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/components/ui/dialog"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Calendar } from "@/components/ui/calendar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Progress } from "@/components/ui/progress"
import { Slider } from "@/components/ui/slider"
import { Badge } from "@/components/ui/badge"
import { CalendarIcon } from "lucide-react"
import { format, formatDistanceToNow } from "date-fns"
import type { Card } from "./kanban-board"
import { Link } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { Plus<PERSON>ir<PERSON>, Trash2 } from "lucide-react"

interface Subtask {
  id: string
  title: string
  completed: boolean
}

interface CardDetailViewProps {
  card: Card
  onClose: () => void
  onUpdate?: (updatedCard: Card) => void
  onDelete?: () => void
}

export function CardDetailView({ card, onClose, onUpdate, onDelete }: CardDetailViewProps) {
  const [title, setTitle] = useState(card.title)
  const [description, setDescription] = useState(card.description)
  // Change the priority state to match the expected values in cardTypes
  const [priority, setPriority] = useState<"low" | "medium" | "high">(
    card.priority && ["low", "medium", "high"].includes(card.priority as string)
      ? (card.priority as "low" | "medium" | "high")
      : "low",
  )
  const [date, setDate] = useState<Date | undefined>(card.dueDate ? new Date(card.dueDate) : undefined)
  const [progress, setProgress] = useState(card.progress || 0)
  const [dependencies, setDependencies] = useState<string[]>(card.dependencies || [])
  const [tags, setTags] = useState<string[]>(card.tags || [])
  const [newTag, setNewTag] = useState("")
  const [activeTab, setActiveTab] = useState("details")
  const [subtasks, setSubtasks] = useState<Subtask[]>(card.subtasks || [])
  const [newSubtask, setNewSubtask] = useState("")

  // Log initial state for debugging
  useEffect(() => {
    console.log("CardDetailView initialized with:", {
      cardId: card.id,
      priority: card.priority,
      tags: card.tags,
    })
  }, [card])

  // Format date for display
  const formatDate = (dateString?: string) => {
    if (!dateString) return null
    const date = new Date(dateString)
    return formatDistanceToNow(date, { addSuffix: true })
  }

  const handleAddSubtask = () => {
    if (newSubtask.trim()) {
      const newSubtaskItem: Subtask = {
        id: `subtask-${Date.now()}`,
        title: newSubtask.trim(),
        completed: false,
      }
      setSubtasks([...subtasks, newSubtaskItem])
      setNewSubtask("")
    }
  }

  const handleToggleSubtask = (id: string) => {
    setSubtasks(
      subtasks.map((subtask) => (subtask.id === id ? { ...subtask, completed: !subtask.completed } : subtask)),
    )
  }

  const handleDeleteSubtask = (id: string) => {
    setSubtasks(subtasks.filter((subtask) => subtask.id !== id))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()

    if (!title.trim()) return

    // Create a new card object with updated values
    const updatedCard: Card = {
      ...card,
      title,
      description,
      priority, // Ensure priority is included
      dueDate: date?.toISOString(),
      progress,
      dependencies,
      tags,
      subtasks,
      updatedAt: new Date().toISOString(),
    }

    console.log("Saving card with priority:", priority)
    console.log("Updated card data:", updatedCard)

    if (onUpdate) {
      onUpdate(updatedCard)
    }

    onClose()
  }

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this card?")) {
      if (typeof onDelete === "function") {
        onDelete()
      }
      onClose()
    }
  }

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      const updatedTags = [...tags, newTag.trim()]
      setTags(updatedTags)
      console.log("Added tag:", newTag.trim(), "New tags:", updatedTags)
      setNewTag("")
    }
  }

  const handleRemoveTag = (tagToRemove: string) => {
    const updatedTags = tags.filter((tag) => tag !== tagToRemove)
    setTags(updatedTags)
    console.log("Removed tag:", tagToRemove, "New tags:", updatedTags)
  }

  return (
    <Dialog open={true} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Card Details</DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="details" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="dependencies">Dependencies</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4 pt-4">
            <form onSubmit={handleSubmit}>
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    value={title}
                    onChange={(e) => setTitle(e.target.value)}
                    placeholder="Enter card title"
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="projectId">Project ID</Label>
                  <Input id="projectId" value={card.projectId} disabled className="bg-muted" />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Enter card description"
                    className="min-h-[100px]"
                  />
                </div>

                <div className="grid gap-2">
                  <Label>Priority</Label>
                  <RadioGroup
                    value={priority}
                    onValueChange={(value) => {
                      console.log("Priority changed to:", value)
                      setPriority(value as "low" | "medium" | "high")
                    }}
                    className="flex flex-wrap gap-4"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="low" id="low" />
                      <Label htmlFor="low" className="text-green-600">
                        Low
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="medium" id="medium" />
                      <Label htmlFor="medium" className="text-yellow-600">
                        Medium
                      </Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="high" id="high" />
                      <Label htmlFor="high" className="text-red-600">
                        High
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
                <div className="text-xs text-muted-foreground mt-1">
                  Current priority: {priority}
                  {card.priority !== priority && " (changed from " + card.priority + ")"}
                </div>

                <div className="grid gap-2">
                  <Label>Tags</Label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {tags.map((tag) => (
                      <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                        {tag}
                        <button
                          type="button"
                          onClick={() => handleRemoveTag(tag)}
                          className="ml-1 text-xs hover:text-destructive"
                        >
                          ×
                        </button>
                      </Badge>
                    ))}
                  </div>
                  <div className="flex gap-2">
                    <Input
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      placeholder="Add a tag"
                      className="flex-1"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          handleAddTag()
                        }
                      }}
                    />
                    <Button type="button" onClick={handleAddTag} size="sm">
                      Add
                    </Button>
                  </div>
                </div>

                <div className="grid gap-2">
                  <Label>Due Date</Label>
                  <div className="flex flex-col space-y-2">
                    <div className="flex items-center gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        className="flex-1 justify-start text-left font-normal"
                        onClick={(e) => {
                          e.preventDefault()
                          // Toggle date picker visibility
                          const datePicker = document.getElementById(`date-picker-${card.id}`)
                          if (datePicker) {
                            datePicker.style.display = datePicker.style.display === "none" ? "block" : "none"
                          }
                        }}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {date ? format(date, "PPP") : "Select a date"}
                      </Button>

                      {date && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={(e) => {
                            e.preventDefault()
                            setDate(undefined)
                          }}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>

                    <div
                      id={`date-picker-${card.id}`}
                      className="border rounded-md p-2 bg-background"
                      style={{ display: "none" }}
                    >
                      <Calendar
                        mode="single"
                        selected={date}
                        onSelect={(newDate) => {
                          setDate(newDate)
                          // Hide calendar after selection
                          const datePicker = document.getElementById(`date-picker-${card.id}`)
                          if (datePicker) {
                            datePicker.style.display = "none"
                          }
                        }}
                        initialFocus
                      />
                    </div>
                  </div>
                </div>

                <div className="grid gap-2">
                  <div className="flex justify-between">
                    <Label>Progress: {progress}%</Label>
                  </div>
                  <Slider
                    value={[progress]}
                    min={0}
                    max={100}
                    step={5}
                    onValueChange={(value) => setProgress(value[0])}
                  />
                  <Progress value={progress} className="h-2" />
                </div>

                {/* Resource usage chart */}
                {card.resourceMetrics && (
                  <div className="grid gap-2 mt-4">
                    <Label>Resource Usage</Label>
                    <div className="bg-muted/30 p-4 rounded-md">
                      <div className="grid grid-cols-3 gap-4">
                        <div className="flex flex-col items-center">
                          <span className="text-sm text-muted-foreground">CPU Time</span>
                          <span className="text-xl font-semibold">{card.resourceMetrics.cpuTime}%</span>
                        </div>
                        <div className="flex flex-col items-center">
                          <span className="text-sm text-muted-foreground">Memory</span>
                          <span className="text-xl font-semibold">{card.resourceMetrics.memoryUsage}MB</span>
                        </div>
                        <div className="flex flex-col items-center">
                          <span className="text-sm text-muted-foreground">Tokens</span>
                          <span className="text-xl font-semibold">{card.resourceMetrics.tokenUsage}k</span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {/* Dependencies summary */}
                {dependencies.length > 0 && (
                  <div className="mt-4">
                    <h3 className="text-sm font-medium mb-2">Dependencies</h3>
                    <div className="space-y-2">
                      {dependencies.map((depId) => (
                        <div key={depId} className="flex items-center justify-between bg-muted/30 p-2 rounded-md">
                          <div className="flex items-center gap-2">
                            <Link className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{`Card ${depId}`}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                <div className="grid gap-2 mt-4">
                  <Label>Subtasks</Label>
                  <div className="space-y-2">
                    {subtasks.map((subtask) => (
                      <div key={subtask.id} className="flex items-center gap-2">
                        <Checkbox
                          id={subtask.id}
                          checked={subtask.completed}
                          onCheckedChange={() => handleToggleSubtask(subtask.id)}
                        />
                        <Label
                          htmlFor={subtask.id}
                          className={`flex-1 text-sm ${subtask.completed ? "line-through text-muted-foreground" : ""}`}
                        >
                          {subtask.title}
                        </Label>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7"
                          onClick={() => handleDeleteSubtask(subtask.id)}
                        >
                          <Trash2 className="h-4 w-4 text-muted-foreground hover:text-destructive" />
                        </Button>
                      </div>
                    ))}
                  </div>
                  <div className="flex gap-2 mt-2">
                    <Input
                      value={newSubtask}
                      onChange={(e) => setNewSubtask(e.target.value)}
                      placeholder="Add a subtask"
                      className="flex-1"
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          e.preventDefault()
                          handleAddSubtask()
                        }
                      }}
                    />
                    <Button type="button" onClick={handleAddSubtask} size="sm">
                      <PlusCircle className="h-4 w-4 mr-1" />
                      Add
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex justify-between mt-6">
                <Button type="button" variant="destructive" onClick={handleDelete}>
                  Delete
                </Button>
                <Button type="submit">Save Changes</Button>
              </div>
            </form>
          </TabsContent>

          <TabsContent value="dependencies" className="space-y-4 pt-4">
            <div className="grid gap-4">
              <div>
                <h3 className="text-lg font-medium mb-2">Task Dependencies</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  Select tasks that must be completed before this task can be started
                </p>

                {/* Simplified dependency selector */}
                <div className="border rounded-md p-4">
                  <p className="text-sm text-muted-foreground">
                    Dependencies can be managed here. In a full implementation, you would see a list of available cards
                    to select as dependencies.
                  </p>
                </div>
              </div>

              {dependencies.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium mb-2">Current Dependencies</h4>
                  <div className="space-y-2">
                    {dependencies.map((depId) => (
                      <div key={depId} className="p-3 border rounded-md">
                        <div className="flex justify-between items-start">
                          <div>
                            <div className="flex items-center gap-2">
                              <h5 className="font-medium">{`Card ${depId}`}</h5>
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDependencies(dependencies.filter((id) => id !== depId))}
                          >
                            Remove
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="flex justify-end mt-6">
              <Button onClick={onClose}>Close</Button>
            </div>
          </TabsContent>

          <TabsContent value="history" className="space-y-4 pt-4">
            <div>
              <h3 className="text-lg font-medium mb-4">Task History</h3>

              {!card.taskHistory || card.taskHistory.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground border rounded-md">
                  No history available for this task
                </div>
              ) : (
                <div className="space-y-4">
                  {card.taskHistory.map((historyItem, index) => (
                    <div key={index} className="relative pl-6 pb-4">
                      {/* Timeline connector */}
                      {index < card.taskHistory.length - 1 && (
                        <div className="absolute left-[0.6rem] top-3 w-0.5 h-full -ml-px bg-muted-foreground/20" />
                      )}

                      {/* Timeline dot */}
                      <div className="absolute left-0 top-1.5 w-3 h-3 rounded-full bg-primary" />

                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">{historyItem.action.replace(/_/g, " ")}</span>
                          <Badge variant="outline" className="text-xs">
                            {historyItem.agentId}
                          </Badge>
                        </div>
                        <span className="text-xs text-muted-foreground">{formatDate(historyItem.timestamp)}</span>
                        <p className="mt-1 text-sm">{historyItem.details}</p>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
