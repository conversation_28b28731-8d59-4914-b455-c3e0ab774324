"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

interface CreateColumnDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateColumn: (title: string) => void
}

export function CreateColumnDialog({ open, onOpenChange, onCreateColumn }: CreateColumnDialogProps) {
  const [columnTitle, setColumnTitle] = useState("")

  const handleCreateColumn = () => {
    if (columnTitle.trim()) {
      onCreateColumn(columnTitle)
      setColumnTitle("")
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add new column</DialogTitle>
          <DialogDescription>Create a new column for your Kanban board.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="column-title" className="text-right">
              Title
            </Label>
            <Input
              id="column-title"
              value={columnTitle}
              onChange={(e) => setColumnTitle(e.target.value)}
              className="col-span-3"
              placeholder="e.g., In Review"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleCreateColumn} disabled={!columnTitle.trim()}>
            Create Column
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
