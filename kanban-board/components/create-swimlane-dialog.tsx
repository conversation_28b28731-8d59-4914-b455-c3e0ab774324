"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"

interface CreateSwimlaneDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onCreateSwimlane: (title: string) => void
}

export function CreateSwimlaneDialog({ open, onOpenChange, onCreateSwimlane }: CreateSwimlaneDialogProps) {
  const [swimlaneTitle, setSwimlanelTitle] = useState("")

  const handleCreateSwimlane = () => {
    if (swimlaneTitle.trim()) {
      onCreateSwimlane(swimlaneTitle)
      setSwimlanelTitle("")
      onOpenChange(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add new swimlane</DialogTitle>
          <DialogDescription>Create a new swimlane for your Kanban board.</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="swimlane-title" className="text-right">
              Title
            </Label>
            <Input
              id="swimlane-title"
              value={swimlaneTitle}
              onChange={(e) => setSwimlanelTitle(e.target.value)}
              className="col-span-3"
              placeholder="e.g., Frontend Tasks"
            />
          </div>
        </div>
        <DialogFooter>
          <Button type="submit" onClick={handleCreateSwimlane} disabled={!swimlaneTitle.trim()}>
            Create Swimlane
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
