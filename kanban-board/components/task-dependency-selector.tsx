"use client"

import { useState, useEffect } from "react"
import { Check, ChevronsUpDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { useBoard } from "./board-context"
import { toast } from "@/components/ui/use-toast"

interface TaskDependencySelectorProps {
  selectedDependencies: string[]
  onChange: (dependencies: string[]) => void
  excludeCardId?: string
  currentCardId: string
  cards: { id: string; title: string; dependencies?: string[] }[]
}

export function TaskDependencySelector({
  selectedDependencies,
  onChange,
  excludeCardId,
  currentCardId,
  cards,
}: TaskDependencySelectorProps) {
  const [open, setOpen] = useState(false)
  const [availableTasks, setAvailableTasks] = useState<{ id: string; title: string }[]>([])
  const boardContext = useBoard()

  // Mock data for demonstration
  useEffect(() => {
    // In a real implementation, this would fetch tasks from the board context
    const mockTasks = [
      { id: "task-1", title: "Setup Database" },
      { id: "task-2", title: "Create API Endpoints" },
      { id: "task-3", title: "Implement Authentication" },
      { id: "task-4", title: "Design UI Components" },
      { id: "task-5", title: "Integrate Frontend with API" },
      { id: "task-6", title: "Write Tests" },
      { id: "task-7", title: "Documentation" },
    ]

    // Filter out the current card and already selected dependencies
    const filteredTasks = mockTasks.filter((task) => task.id !== excludeCardId)

    setAvailableTasks(filteredTasks)
  }, [excludeCardId])

  // Toggle a dependency
  const toggleDependency = (taskId: string) => {
    if (selectedDependencies.includes(taskId)) {
      onChange(selectedDependencies.filter((id) => id !== taskId))
    } else {
      onChange([...selectedDependencies, taskId])
    }
  }

  // Remove a dependency
  const removeDependency = (taskId: string) => {
    onChange(selectedDependencies.filter((id) => id !== taskId))
  }

  // Get task title by ID
  const getTaskTitle = (taskId: string) => {
    const task = availableTasks.find((t) => t.id === taskId)
    return task ? task.title : taskId
  }

  // Add a check for circular dependencies when selecting a dependency
  const handleSelect = (cardId: string) => {
    // Check if adding this dependency would create a circular reference
    const wouldCreateCircular = checkForCircularDependency(cardId, selectedDependencies)

    if (wouldCreateCircular) {
      toast({
        title: "Cannot add dependency",
        description: "This would create a circular dependency chain",
        variant: "destructive",
      })
      return
    }

    // Add the dependency
    const newValue = [...selectedDependencies, cardId]
    onChange(newValue)
    setOpen(false)
  }

  // Helper function to check for circular dependencies
  const checkForCircularDependency = (newDependencyId: string, currentDependencies: string[]): boolean => {
    // If the card we're adding as a dependency already depends on our card, it's circular
    const dependencyCard = cards.find((card) => card.id === newDependencyId)
    if (!dependencyCard || !dependencyCard.dependencies) return false

    // Direct circular dependency
    if (dependencyCard.dependencies && dependencyCard.dependencies.includes(currentCardId)) return true

    // Check for indirect circular dependencies (A depends on B, B depends on C, C depends on A)
    const checkCard = (cardId: string, visited: Set<string>): boolean => {
      if (visited.has(cardId)) return false // Already checked this path
      visited.add(cardId)

      const card = cards.find((c) => c.id === cardId)
      if (!card || !card.dependencies) return false

      // If any dependency leads back to our card, it's circular
      if (card.dependencies && card.dependencies.includes(currentCardId)) return true

      // Check each dependency recursively
      return card.dependencies ? card.dependencies.some((depId) => checkCard(depId, new Set(visited))) : false
    }

    return checkCard(newDependencyId, new Set<string>())
  }

  return (
    <div className="space-y-2">
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button variant="outline" role="combobox" aria-expanded={open} className="w-full justify-between">
            Select dependencies
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0">
          <Command>
            <CommandInput placeholder="Search tasks..." />
            <CommandList>
              <CommandEmpty>No tasks found.</CommandEmpty>
              <CommandGroup>
                {availableTasks.map((task) => (
                  <CommandItem
                    key={task.id}
                    value={task.id}
                    onSelect={() => {
                      handleSelect(task.id)
                    }}
                  >
                    <Check
                      className={cn(
                        "mr-2 h-4 w-4",
                        selectedDependencies.includes(task.id) ? "opacity-100" : "opacity-0",
                      )}
                    />
                    {task.title}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {selectedDependencies.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {selectedDependencies.map((depId) => (
            <Badge key={depId} variant="secondary" className="flex items-center gap-1">
              {getTaskTitle(depId)}
              <button className="ml-1 rounded-full hover:bg-muted p-0.5" onClick={() => removeDependency(depId)}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
