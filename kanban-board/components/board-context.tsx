"use client"

import type React from "react"
import { create<PERSON>ontext, useContext, useState, useEffect } from "react"
import { useToast } from "@/components/ui/use-toast"
import { useTheme } from "next-themes"
import { AgentStatus } from "../../file-explorer/components/agents/agent-base" // Import shared AgentStatus

// Define shared types for board data structure
export type CardType = {
  id: string
  name: string
  color: string
}

export type AgentAssignment = {
  agentId: string
  agentType: string
  assignmentTime: string
}

export type ResourceMetrics = {
  tokenUsage: number
  cpuTime: number
  memoryUsage: number
}

export type TaskHistoryItem = {
  timestamp: string
  action: string
  agentId: string
  details: string
}

export type Card = {
  id: string
  title: string
  description: string
  priority: string // Changed from "low" | "medium" | "high" to string
  dueDate?: string
  labels: CardType[]
  assignee?: string
  attachments?: string[]
  comments?: { id: string; author: string; text: string; timestamp: string }[]
  progress: number
  columnId: string
  swimlaneId?: string
  projectId?: string
  tags?: string[]
  subtasks?: { id: string; title: string; completed: boolean }[]
  agentAssignments: AgentAssignment[]
  dependencies: string[]
  resourceMetrics: ResourceMetrics
  taskHistory: TaskHistoryItem[]
  storyPoints?: number
  updatedAt: string
  createdAt: string
}

export type Column = {
  id: string
  title: string
  cards: Card[]
  limit?: number
  subColumns?: { id: string; title: string }[]
  metadata?: any
}

export type Swimlane = {
  id: string
  title: string
  isExpanded: boolean
}

// Use the imported AgentStatus as the Agent type for the board context
export type Agent = AgentStatus;

// Enhanced board type to store complete board data
export type BoardFull = {
  id: string
  name: string
  description?: string
  columns: Column[]
  swimlanes: Swimlane[]
  cardTypes: CardType[]
  agents: Agent[]
}

// Define the context type with comprehensive operations
type BoardContextType = {
  boards: BoardFull[]
  activeBoard: BoardFull | null
  setActiveBoard: (boardId: string) => void
  addBoard: (name: string) => BoardFull
  updateBoard: (id: string, name: string, description?: string) => void
  deleteBoard: (id: string) => void

  // Column operations
  addColumn: (boardId: string, title: string) => string
  updateColumn: (boardId: string, column: Column) => void
  deleteColumn: (boardId: string, columnId: string) => void
  moveColumn: (boardId: string, dragId: string, overId: string | null) => void // Added moveColumn to context type

  // Swimlane operations
  addSwimlane: (boardId: string, title: string) => string
  updateSwimlane: (boardId: string, swimlane: Swimlane) => void
  deleteSwimlane: (boardId: string, swimlaneId: string) => void
  toggleSwimlaneExpansion: (boardId: string, swimlaneId: string) => void

  // Card operations
  addCardToColumn: (boardId: string, columnId: string, cardData: Omit<Card, "id" | "columnId" | "createdAt" | "updatedAt">) => string // Adjusted cardData type
  updateCardInColumn: (boardId: string, updatedCard: Card) => void // Removed columnId from params, it should be on card
  deleteCardFromColumn: (boardId: string, columnId: string, cardId: string) => void
  moveCard: (
    boardId: string,
    cardId: string,
    sourceColumnId: string,
    destinationColumnId: string,
    destinationSwimlaneId: string,
  ) => void

  // Card Type / Legend operations
  updateCardTypes: (boardId: string, cardTypes: CardType[]) => void
  // Agent operations
  updateAgents: (boardId: string, agents: Agent[]) => void
}

// Create the context with default values
const BoardContext = createContext<BoardContextType | undefined>(undefined)

// Default data for new boards
const defaultCardTypes: CardType[] = [
  { id: "low", name: "Low", color: "#22c55e" },
  { id: "medium", name: "Medium", color: "#facc15" },
  { id: "high", name: "High", color: "#ef4444" },
]

const defaultColumns: Column[] = [
  {
    id: "column-1",
    title: "Backlog",
    cards: [],
  },
  {
    id: "column-2",
    title: "Ready",
    cards: [],
  },
  {
    id: "column-3",
    title: "In Development",
    cards: [],
  },
  {
    id: "column-4",
    title: "In Review",
    cards: [],
  },
  {
    id: "column-5",
    title: "Testing / QA",
    cards: [],
  },
  {
    id: "column-6",
    title: "Done",
    cards: [],
  },
]

const defaultSwimlanes: Swimlane[] = [
  {
    id: "swimlane-1",
    title: "Default Swimlane",
    isExpanded: true,
  },
]

// Default agents should be an empty array as they will be populated by the agent manager
const defaultAgents: Agent[] = []

// Sample data for initial boards
const initialSampleCards: Card[] = [
  {
    id: "card-1",
    title: "Setup Database",
    description: "Configure the database for the application",
    priority: "low",
    projectId: "TASK-123",
    tags: ["database", "setup"],
    subtasks: [],
    progress: 0,
    columnId: "column-1",
    swimlaneId: "swimlane-1",
    agentAssignments: [],
    dependencies: [],
    resourceMetrics: {
      tokenUsage: 0,
      cpuTime: 0,
      memoryUsage: 0,
    },
    taskHistory: [],
    storyPoints: 3,
    updatedAt: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    labels: [],
  },
  {
    id: "card-2",
    title: "Create API Endpoints",
    description: "Develop RESTful API endpoints for data access",
    priority: "medium",
    projectId: "TASK-124",
    tags: ["api", "backend"],
    subtasks: [],
    progress: 0,
    columnId: "column-2",
    swimlaneId: "swimlane-1",
    agentAssignments: [],
    dependencies: [],
    resourceMetrics: {
      tokenUsage: 0,
      cpuTime: 0,
      memoryUsage: 0,
    },
    taskHistory: [],
    storyPoints: 5,
    updatedAt: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    labels: [],
  },
  {
    id: "card-3",
    title: "Implement Authentication",
    description: "Implement user authentication and authorization",
    priority: "high",
    projectId: "TASK-125",
    tags: ["auth", "security"],
    subtasks: [],
    progress: 0,
    columnId: "column-3",
    swimlaneId: "swimlane-1",
    agentAssignments: [],
    dependencies: [],
    resourceMetrics: {
      tokenUsage: 0,
      cpuTime: 0,
      memoryUsage: 0,
    },
    taskHistory: [],
    storyPoints: 8,
    updatedAt: new Date().toISOString(),
    createdAt: new Date().toISOString(),
    labels: [],
  },
]

// Helper to populate columns with cards
const populateColumnsWithCards = (columns: Column[], cards: Card[]): Column[] => {
  const columnMap: Record<string, Card[]> = {}

  // Group cards by columnId
  cards.forEach((card) => {
    if (!columnMap[card.columnId]) {
      columnMap[card.columnId] = []
    }
    columnMap[card.columnId].push(card)
  })

  // Add cards to their respective columns
  return columns.map((column) => {
    const columnCards = columnMap[column.id] || []
    return { ...column, cards: columnCards }
  })
}

// Initial boards with full structure
const initialFullBoards: BoardFull[] = [
  {
    id: "main",
    name: "Main Development Board",
    columns: populateColumnsWithCards(
      JSON.parse(JSON.stringify(defaultColumns)),
      JSON.parse(JSON.stringify(initialSampleCards)),
    ),
    swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
    cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
    agents: JSON.parse(JSON.stringify(defaultAgents)),
  },
  {
    id: "frontend",
    name: "Frontend Tasks",
    columns: JSON.parse(JSON.stringify(defaultColumns.map((col) => ({ ...col, cards: [] })))),
    swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
    cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
    agents: JSON.parse(JSON.stringify(defaultAgents)),
  },
  {
    id: "backend",
    name: "Backend Tasks",
    columns: JSON.parse(JSON.stringify(defaultColumns.map((col) => ({ ...col, cards: [] })))),
    swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
    cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
    agents: JSON.parse(JSON.stringify(defaultAgents)),
  },
]

// Provider component
export function BoardProvider({ children }: { children: React.ReactNode }) {
  const [boards, setBoardsState] = useState<BoardFull[]>(initialFullBoards)
  const [activeBoard, setActiveBoardState] = useState<BoardFull | null>(
    initialFullBoards.length > 0 ? initialFullBoards[0] : null,
  )
  const { toast } = useToast()
  const { setTheme } = useTheme()

  // Initialize theme only once on mount
  useEffect(() => {
    // Check if theme is already set in localStorage
    const savedTheme = localStorage.getItem("theme")
    if (savedTheme) {
      setTheme(savedTheme)

      // Also directly manipulate the DOM for immediate feedback
      if (savedTheme === "dark") {
        document.documentElement.classList.add("dark")
      } else {
        document.documentElement.classList.remove("dark")
      }
    } else {
      // Check for system preference
      if (typeof window !== "undefined") {
        const prefersDark = window.matchMedia("(prefers-color-scheme: dark)").matches
        setTheme(prefersDark ? "dark" : "light")

        // Also directly manipulate the DOM
        if (prefersDark) {
          document.documentElement.classList.add("dark")
        } else {
          document.documentElement.classList.remove("dark")
        }
      }
    }
    // Empty dependency array ensures this only runs once on mount
  }, [])

  // Set active board by ID
  const setActiveBoard = (boardId: string) => {
    const board = boards.find((b) => b.id === boardId)
    if (board) {
      setActiveBoardState(board)
    } else {
      // Fallback if board not found
      if (boards.length > 0) setActiveBoardState(boards[0])
      else setActiveBoardState(null)
    }
  }

  // Add a new board with default structure
  const addBoard = (name: string): BoardFull => {
    const id = name.toLowerCase().replace(/\s+/g, "-") + `-${Date.now()}`
    const newBoard: BoardFull = {
      id,
      name,
      columns: JSON.parse(JSON.stringify(defaultColumns.map((col) => ({ ...col, cards: [] })))),
      swimlanes: JSON.parse(JSON.stringify(defaultSwimlanes)),
      cardTypes: JSON.parse(JSON.stringify(defaultCardTypes)),
      agents: JSON.parse(JSON.stringify(defaultAgents)),
      description: "",
    }
    setBoardsState([...boards, newBoard])
    toast({
      title: "Board created",
      description: `Board "${name}" has been created.`,
    })
    return newBoard
  }

  // Update a board
  const updateBoard = (id: string, name: string, description?: string) => {
    setBoardsState(
      boards.map((board) =>
        board.id === id ? { ...board, name, description: description ?? board.description } : board,
      ),
    )
    if (activeBoard && activeBoard.id === id) {
      setActiveBoardState((prev) => (prev ? { ...prev, name, description: description ?? prev.description } : null))
    }
    toast({
      title: "Board updated",
      description: `Board has been renamed to "${name}".`,
    })
  }

  // Delete a board
  const deleteBoard = (id: string) => {
    if (boards.length <= 1) {
      toast({
        title: "Cannot delete board",
        description: "You must have at least one board.",
        variant: "destructive",
      })
      return
    }
    const updated = boards.filter((board) => board.id !== id)
    setBoardsState(updated)
    if (activeBoard && activeBoard.id === id) {
      setActiveBoard(updated.length > 0 ? updated[0].id : "")
    }
    toast({
      title: "Board deleted",
      description: "The board has been deleted.",
    })
  }

  // Add a new column
  const addColumn = (boardId: string, title: string): string => {
    const newColumnId = `column-${Date.now()}`
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          const newColumn: Column = { id: newColumnId, title, cards: [] }
          return { ...board, columns: [...board.columns, newColumn] }
        }
        return board
      }),
    )

    // Update activeBoard if it's the one being modified
    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        const newColumn: Column = { id: newColumnId, title, cards: [] }
        return { ...prev, columns: [...prev.columns, newColumn] }
      })
    }

    toast({
      title: "Column added",
      description: `Column "${title}" has been added.`,
    })

    return newColumnId
  }

  // Update a column
  const updateColumn = (boardId: string, updatedColumn: Column) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => (col.id === updatedColumn.id ? updatedColumn : col)),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              columns: prev.columns.map((col) => (col.id === updatedColumn.id ? updatedColumn : col)),
            }
          : null,
      )
    }

    toast({
      title: "Column updated",
      description: `Column "${updatedColumn.title}" has been updated.`,
    })
  }

  // Delete a column
  const deleteColumn = (boardId: string, columnId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return { ...board, columns: board.columns.filter((col) => col.id !== columnId) }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              columns: prev.columns.filter((col) => col.id !== columnId),
            }
          : null,
      )
    }

    toast({
      title: "Column deleted",
      description: "The column has been deleted.",
    })
  }

  // Add a new swimlane
  const addSwimlane = (boardId: string, title: string): string => {
    const newSwimlaneId = `swimlane-${Date.now()}`
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          const newSwimlane: Swimlane = { id: newSwimlaneId, title, isExpanded: true }
          return { ...b, swimlanes: [...b.swimlanes, newSwimlane] }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        const newSwimlane: Swimlane = { id: newSwimlaneId, title, isExpanded: true }
        return { ...prev, swimlanes: [...prev.swimlanes, newSwimlane] }
      })
    }

    toast({
      title: "Swimlane added",
      description: `Swimlane "${title}" has been added.`,
    })

    return newSwimlaneId
  }

  // Update a swimlane
  const updateSwimlane = (boardId: string, updatedSwimlane: Swimlane) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return { ...b, swimlanes: b.swimlanes.map((s) => (s.id === updatedSwimlane.id ? updatedSwimlane : s)) }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.map((s) => (s.id === updatedSwimlane.id ? updatedSwimlane : s)),
            }
          : null,
      )
    }

    toast({
      title: "Swimlane updated",
      description: `Swimlane "${updatedSwimlane.title}" has been updated.`,
    })
  }

  // Delete a swimlane
  const deleteSwimlane = (boardId: string, swimlaneId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return { ...b, swimlanes: b.swimlanes.filter((s) => s.id !== swimlaneId) }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.filter((s) => s.id !== swimlaneId),
            }
          : null,
      )
    }

    toast({
      title: "Swimlane deleted",
      description: "The swimlane has been deleted.",
    })
  }

  // Toggle swimlane expansion
  const toggleSwimlaneExpansion = (boardId: string, swimlaneId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((b) => {
        if (b.id === boardId) {
          return {
            ...b,
            swimlanes: b.swimlanes.map((s) => (s.id === swimlaneId ? { ...s, isExpanded: !s.isExpanded } : s)),
          }
        }
        return b
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) =>
        prev
          ? {
              ...prev,
              swimlanes: prev.swimlanes.map((s) => (s.id === swimlaneId ? { ...s, isExpanded: !s.isExpanded } : s)),
            }
          : null,
      )
    }
  }

  // Add a card to a column
  const addCardToColumn = (boardId: string, columnId: string, cardData: Omit<Card, "id" | "columnId" | "createdAt" | "updatedAt">): string => {
    const now = new Date().toISOString()
    const newCardId = `card-${Date.now()}`

    const newCard: Card = {
      ...cardData,
      id: newCardId,
      columnId,
      createdAt: now,
      updatedAt: now,
      taskHistory: [
        ...(cardData.taskHistory || []),
        {
          timestamp: now,
          action: "created",
          agentId: "user", // Default to 'user' for manually created cards
          details: "Card created",
        },
      ],
      // Ensure other fields have defaults if they were Omitted but are non-optional in Card type
      labels: cardData.labels || [],
      agentAssignments: cardData.agentAssignments || [],
      dependencies: cardData.dependencies || [],
      resourceMetrics: cardData.resourceMetrics || { tokenUsage: 0, cpuTime: 0, memoryUsage: 0 },
      progress: cardData.progress || 0,
      priority: cardData.priority || "medium", // Ensure default priority if not provided
    }

    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => {
              if (col.id === columnId) {
                return { ...col, cards: [...col.cards, newCard] }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        return {
          ...prev,
          columns: prev.columns.map((col) => {
            if (col.id === columnId) {
              return { ...col, cards: [...col.cards, newCard] }
            }
            return col
          }),
        }
      })
    }

    toast({
      title: "Card added",
      description: `Card "${newCard.title}" has been added.`,
    })

    return newCardId
  }

  // Update a card in a column
  const updateCardInColumn = (boardId: string, updatedCard: Card) => { // Removed columnId from params
    console.log("Updating card in context:", {
      cardId: updatedCard.id,
      priority: updatedCard.priority,
      tags: updatedCard.tags,
      columnIdFromCard: updatedCard.columnId, // Get columnId from the card object itself
      fullCard: updatedCard,
    })

    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => {
              // Check if the card is in this column and its columnId matches the updated card's columnId
              // This handles cases where only a card's property (not columnId) is updated
              if (col.id === updatedCard.columnId) {
                return {
                  ...col,
                  cards: col.cards.map((c) =>
                    c.id === updatedCard.id ? { ...updatedCard, updatedAt: new Date().toISOString() } : c,
                  ),
                }
              }
              // If the card was moved to a different column by the update, remove it from the old column
              const cardExistsInOtherColumn = col.cards.some(c => c.id === updatedCard.id);
              if (cardExistsInOtherColumn && col.id !== updatedCard.columnId) {
                return {
                  ...col,
                  cards: col.cards.filter(c => c.id !== updatedCard.id)
                }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        return {
          ...prev,
          columns: prev.columns.map((col) => {
            if (col.id === updatedCard.columnId) {
              return {
                ...col,
                cards: col.cards.map((c) =>
                  c.id === updatedCard.id ? { ...updatedCard, updatedAt: new Date().toISOString() } : c,
                ),
              }
            }
            const cardExistsInOtherColumn = col.cards.some(c => c.id === updatedCard.id);
            if (cardExistsInOtherColumn && col.id !== updatedCard.columnId) {
              return {
                ...col,
                cards: col.cards.filter(c => c.id !== updatedCard.id)
              }
            }
            return col
          }),
        }
      })
    }

    toast({
      title: "Card updated",
      description: `Card "${updatedCard.title}" has been updated.`,
    })
  }

  // Delete a card from a column
  const deleteCardFromColumn = (boardId: string, columnId: string, cardId: string) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return {
            ...board,
            columns: board.columns.map((col) => {
              if (col.id === columnId) {
                return { ...col, cards: col.cards.filter((c) => c.id !== cardId) }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => {
        if (!prev) return null
        return {
          ...prev,
          columns: prev.columns.map((col) => {
            if (col.id === columnId) {
              return { ...col, cards: col.cards.filter((c) => c.id !== cardId) }
            }
            return col
          }),
        }
      })
    }

    toast({
      title: "Card deleted",
      description: "The card has been deleted.",
    })
  }

  // Move a card between columns
  const moveCard = (
    boardId: string,
    cardId: string,
    sourceColumnId: string,
    destinationColumnId: string,
    destinationSwimlaneId: string,
  ) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          let cardToMove: Card | undefined

          // Find and remove card from source column
          const updatedColumns = board.columns.map((col) => {
            if (col.id === sourceColumnId) {
              const cardIndex = col.cards.findIndex((c) => c.id === cardId)
              if (cardIndex > -1) {
                cardToMove = col.cards[cardIndex]
                return {
                  ...col,
                  cards: col.cards.filter((c) => c.id !== cardId),
                }
              }
            }
            return col
          })

          if (!cardToMove) {
            console.warn(`Card ${cardId} not found in source column ${sourceColumnId}. Board state not updated.`);
            return board; // Card not found, return original board state
          }

          // Update card with new swimlaneId and columnId
          const updatedCard = {
            ...cardToMove,
            swimlaneId: destinationSwimlaneId,
            columnId: destinationColumnId,
            updatedAt: new Date().toISOString(),
            taskHistory: [
              ...cardToMove.taskHistory,
              {
                timestamp: new Date().toISOString(),
                action: "moved",
                agentId: "user",
                details: `Moved from ${sourceColumnId} to ${destinationColumnId}`,
              },
            ],
          }

          // Add card to destination column
          return {
            ...board,
            columns: updatedColumns.map((col) => { // Use updatedColumns to avoid adding back to old if it wasn't there
              if (col.id === destinationColumnId) {
                return {
                  ...col,
                  cards: [...col.cards, updatedCard],
                }
              }
              return col
            }),
          }
        }
        return board
      }),
    )

    // Update activeBoard if it's the one being modified
    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prevActiveBoard) => {
        if (!prevActiveBoard) return null

        let cardToMove: Card | undefined

        // Find and remove card from source column
        const updatedColumns = prevActiveBoard.columns.map((col) => {
          if (col.id === sourceColumnId) {
            const cardIndex = col.cards.findIndex((c) => c.id === cardId)
            if (cardIndex > -1) {
              cardToMove = col.cards[cardIndex]
              return {
                ...col,
                cards: col.cards.filter((c) => c.id !== cardId),
              }
            }
          }
          return col
        })

        if (!cardToMove) {
          console.warn(`Card ${cardId} not found in source column ${sourceColumnId} of active board. Active board state not updated.`);
          return prevActiveBoard;
        }

        // Update card with new swimlaneId and columnId
        const updatedCard = {
          ...cardToMove,
          swimlaneId: destinationSwimlaneId,
          columnId: destinationColumnId,
          updatedAt: new Date().toISOString(),
          taskHistory: [
            ...cardToMove.taskHistory,
            {
              timestamp: new Date().toISOString(),
              action: "moved",
              agentId: "user",
              details: `Moved from ${sourceColumnId} to ${destinationColumnId}`,
            },
          ],
        }

        // Add card to destination column
        return {
          ...prevActiveBoard,
          columns: updatedColumns.map((col) => {
            if (col.id === destinationColumnId) {
              return {
                ...col,
                cards: [...col.cards, updatedCard],
              }
            }
            return col
          }),
        }
      })
    }

    toast({
      title: "Card moved",
      description: "The card has been moved.",
    })
  }

  // Update card types (legend)
  const updateCardTypes = (boardId: string, newCardTypes: CardType[]) => {
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => (board.id === boardId ? { ...board, cardTypes: newCardTypes } : board)),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => (prev ? { ...prev, cardTypes: newCardTypes } : null))
    }

    toast({
      title: "Legend updated",
      description: "The card types have been updated.",
    })
  }

  // Update agents
  const updateAgents = (boardId: string, newAgents: Agent[]) => { // Agent type now refers to shared AgentStatus
    setBoardsState((prevBoards) =>
      prevBoards.map((board) => (board.id === boardId ? { ...board, agents: newAgents } : board)),
    )

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prev) => (prev ? { ...prev, agents: newAgents } : null))
    }

    // Only toast if there's a significant change, not just constant updates
    // toast({
    //   title: "Agents updated",
    //   description: "The agents have been updated.",
    // });
  }

  // Move a column (added from kanban-board)
  const moveColumn = (boardId: string, dragId: string, overId: string | null) => {
    const reorderLogic = (currentColumns: Column[]): Column[] => {
      const oldIndex = currentColumns.findIndex((col) => col.id === dragId);
      if (oldIndex === -1) {
        console.warn(`moveColumn: dragId ${dragId} not found in columns.`);
        return currentColumns;
      }

      const newColumnsArray = [...currentColumns];
      const [movedColumn] = newColumnsArray.splice(oldIndex, 1);

      if (overId === null) {
        newColumnsArray.push(movedColumn);
      } else {
        const newIndexTarget = newColumnsArray.findIndex((col) => col.id === overId);
        if (newIndexTarget === -1) { // Fallback if target not found (e.g., trying to move onto self, already handled by DNDKit)
          newColumnsArray.push(movedColumn);
        } else {
          newColumnsArray.splice(newIndexTarget, 0, movedColumn);
        }
      }
      return newColumnsArray;
    };

    setBoardsState((prevBoards) =>
      prevBoards.map((board) => {
        if (board.id === boardId) {
          return { ...board, columns: reorderLogic(board.columns) };
        }
        return board;
      })
    );

    if (activeBoard && activeBoard.id === boardId) {
      setActiveBoardState((prevActiveBoard) => {
        if (!prevActiveBoard) return null;
        return { ...prevActiveBoard, columns: reorderLogic(prevActiveBoard.columns) };
      });
    }

    toast({
      title: "Column moved",
      description: "The column order has been updated.",
    });
  };

  return (
    <BoardContext.Provider
      value={{
        boards,
        activeBoard,
        setActiveBoard,
        addBoard,
        updateBoard,
        deleteBoard,
        addColumn,
        updateColumn,
        deleteColumn,
        moveColumn, // Added to context provider
        addSwimlane,
        updateSwimlane,
        deleteSwimlane,
        toggleSwimlaneExpansion,
        addCardToColumn,
        updateCardInColumn,
        deleteCardFromColumn,
        moveCard,
        updateCardTypes,
        updateAgents,
      }}
    >
      {children}
    </BoardContext.Provider>
  )
}

// Custom hook to use the board context
export function useBoard() {
  const context = useContext(BoardContext)
  if (context === undefined) {
    throw new Error("useBoard must be used within a BoardProvider")
  }
  return context
}

// Remove redundant useBoardContext
export function useBoardContext() {
  return useBoard()
}