"use client"

import { useState, useContext } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AgentBoardControllerContext } from "./agent-board-controller"

export function AnalyticsDashboard() {
  const [timeRange, setTimeRange] = useState("7d")

  // Safely access the context - if it doesn't exist, we'll use an empty array
  const agentContext = useContext(AgentBoardControllerContext)
  const agents = agentContext?.agents || []

  // Mock data for demonstration
  const taskCompletionData = {
    "7d": [4, 6, 8, 7, 9, 10, 12],
    "30d": [
      4, 6, 8, 7, 9, 10, 12, 14, 13, 15, 17, 16, 18, 20, 19, 21, 22, 24, 23, 25, 27, 26, 28, 30, 29, 31, 32, 34, 33, 35,
    ],
    "90d": Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 30) + 10),
  }

  const agentPerformanceData = {
    TaskBot: { tasksCompleted: 45, successRate: 0.92, avgCompletionTime: 120 },
    CodeAssistant: { tasksCompleted: 38, successRate: 0.89, avgCompletionTime: 180 },
    QATester: { tasksCompleted: 52, successRate: 0.95, avgCompletionTime: 90 },
    DocWriter: { tasksCompleted: 29, successRate: 0.97, avgCompletionTime: 150 },
  }

  const resourceUtilizationData = {
    "7d": {
      cpu: [25, 30, 28, 35, 40, 38, 42],
      memory: [128, 156, 142, 168, 180, 175, 190],
      tokens: [5000, 7500, 6800, 8200, 9500, 9000, 10200],
    },
    "30d": {
      cpu: Array.from({ length: 30 }, (_, i) => Math.floor(Math.random() * 30) + 20),
      memory: Array.from({ length: 30 }, (_, i) => Math.floor(Math.random() * 100) + 100),
      tokens: Array.from({ length: 30 }, (_, i) => Math.floor(Math.random() * 5000) + 5000),
    },
    "90d": {
      cpu: Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 30) + 20),
      memory: Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 100) + 100),
      tokens: Array.from({ length: 90 }, (_, i) => Math.floor(Math.random() * 5000) + 5000),
    },
  }

  // Render a simple bar chart
  const renderBarChart = (data: number[], height = 150, color = "#2563eb") => {
    const max = Math.max(...data)

    return (
      <div className="flex items-end h-[150px] gap-1">
        {data.map((value, index) => (
          <div
            key={index}
            className="flex-1 rounded-t"
            style={{
              height: `${(value / max) * height}px`,
              backgroundColor: color,
            }}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Analytics Dashboard</h2>
        <Select value={timeRange} onValueChange={setTimeRange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select time range" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="7d">Last 7 days</SelectItem>
            <SelectItem value="30d">Last 30 days</SelectItem>
            <SelectItem value="90d">Last 90 days</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Tabs defaultValue="task-completion">
        <TabsList className="grid grid-cols-3">
          <TabsTrigger value="task-completion">Task Completion</TabsTrigger>
          <TabsTrigger value="agent-performance">Agent Performance</TabsTrigger>
          <TabsTrigger value="resource-utilization">Resource Utilization</TabsTrigger>
        </TabsList>

        <TabsContent value="task-completion" className="mt-4">
          <Card>
            <CardHeader>
              <CardTitle>Task Completion Velocity</CardTitle>
              <CardDescription>Number of tasks completed over time</CardDescription>
            </CardHeader>
            <CardContent>
              {renderBarChart(taskCompletionData[timeRange as keyof typeof taskCompletionData], 150, "#4CAF50")}
              <div className="flex justify-between mt-2 text-xs text-muted-foreground">
                {timeRange === "7d" && (
                  <>
                    <div>Mon</div>
                    <div>Tue</div>
                    <div>Wed</div>
                    <div>Thu</div>
                    <div>Fri</div>
                    <div>Sat</div>
                    <div>Sun</div>
                  </>
                )}
                {timeRange === "30d" && (
                  <>
                    <div>Week 1</div>
                    <div>Week 2</div>
                    <div>Week 3</div>
                    <div>Week 4</div>
                    <div>Week 5</div>
                  </>
                )}
                {timeRange === "90d" && (
                  <>
                    <div>Month 1</div>
                    <div>Month 2</div>
                    <div>Month 3</div>
                  </>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="agent-performance" className="mt-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(agentPerformanceData).map(([agentName, data]) => (
              <Card key={agentName}>
                <CardHeader>
                  <CardTitle>{agentName}</CardTitle>
                  <CardDescription>Performance metrics</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Tasks Completed</span>
                      <span className="font-medium">{data.tasksCompleted}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Success Rate</span>
                      <span className="font-medium">{(data.successRate * 100).toFixed(1)}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Avg. Completion Time</span>
                      <span className="font-medium">{data.avgCompletionTime} min</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="resource-utilization" className="mt-4">
          <div className="grid grid-cols-1 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>CPU Usage</CardTitle>
                <CardDescription>Average CPU usage percentage</CardDescription>
              </CardHeader>
              <CardContent>
                {renderBarChart(
                  resourceUtilizationData[timeRange as keyof typeof resourceUtilizationData].cpu,
                  150,
                  "#F44336",
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Memory Usage</CardTitle>
                <CardDescription>Memory consumption in MB</CardDescription>
              </CardHeader>
              <CardContent>
                {renderBarChart(
                  resourceUtilizationData[timeRange as keyof typeof resourceUtilizationData].memory,
                  150,
                  "#2196F3",
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Token Usage</CardTitle>
                <CardDescription>API tokens consumed</CardDescription>
              </CardHeader>
              <CardContent>
                {renderBarChart(
                  resourceUtilizationData[timeRange as keyof typeof resourceUtilizationData].tokens,
                  150,
                  "#9C27B0",
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
