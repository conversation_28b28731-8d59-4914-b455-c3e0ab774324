"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import type { CardType } from "./kanban-board"

interface LegendEditDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  cardTypes: CardType[]
  onSave: (cardTypes: CardType[]) => void
}

export function LegendEditDialog({ open, onOpenChange, cardTypes, onSave }: LegendEditDialogProps) {
  const [editedCardTypes, setEditedCardTypes] = useState<CardType[]>(() => cardTypes.map((type) => ({ ...type })))

  const handleNameChange = (id: string, name: string) => {
    setEditedCardTypes((prev) => prev.map((type) => (type.id === id ? { ...type, name } : type)))
  }

  const handleColorChange = (id: string, color: string) => {
    setEditedCardTypes((prev) => prev.map((type) => (type.id === id ? { ...type, color } : type)))
  }

  const handleSave = () => {
    onSave(editedCardTypes)
    onOpenChange(false)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Card Types</DialogTitle>
        </DialogHeader>
        <div className="space-y-4 py-4">
          {editedCardTypes.map((type) => (
            <div key={type.id} className="flex items-center gap-4">
              <div className="flex-1">
                <Label htmlFor={`name-${type.id}`}>Name</Label>
                <Input
                  id={`name-${type.id}`}
                  value={type.name}
                  onChange={(e) => handleNameChange(type.id, e.target.value)}
                />
              </div>
              <div className="w-24">
                <Label htmlFor={`color-${type.id}`}>Color</Label>
                <div className="flex items-center gap-2">
                  <Input
                    id={`color-${type.id}`}
                    type="color"
                    value={type.color}
                    onChange={(e) => handleColorChange(type.id, e.target.value)}
                    className="w-12 h-8 p-1"
                  />
                  <div className="w-6 h-6 rounded-sm border border-gray-300" style={{ backgroundColor: type.color }} />
                </div>
              </div>
            </div>
          ))}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save Changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
