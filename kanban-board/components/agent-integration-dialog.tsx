"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogDescription,
  Dialog<PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Bot, BrainCircuit, Cpu, GitBranch, Settings } from "lucide-react"
import { Separator } from "@/components/ui/separator"
import { useToast } from "@/components/ui/use-toast"

export function AgentIntegrationDialog() {
  const [agentType, setAgentType] = useState<string>("task-manager")
  const [autoAssign, setAutoAssign] = useState<boolean>(true)
  const [resourceLimit, setResourceLimit] = useState<number>(50)
  const [isOpen, setIsOpen] = useState<boolean>(false)
  const { toast } = useToast()

  const handleSave = () => {
    toast({
      title: "Agent settings saved",
      description: "Your AI agent integration settings have been updated.",
    })
    setIsOpen(false)
  }

  return (
    <>
      <Button variant="outline" className="gap-2" onClick={() => setIsOpen(true)}>
        <BrainCircuit className="h-4 w-4" />
        AI Agent Settings
      </Button>

      {isOpen && (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>AI Agent Integration</DialogTitle>
              <DialogDescription>Configure how AI agents interact with your Kanban board</DialogDescription>
            </DialogHeader>

            <Tabs defaultValue="agents" className="w-full">
              <TabsList className="grid grid-cols-3 mb-4">
                <TabsTrigger value="agents">
                  <Bot className="h-4 w-4 mr-2" />
                  Agents
                </TabsTrigger>
                <TabsTrigger value="resources">
                  <Cpu className="h-4 w-4 mr-2" />
                  Resources
                </TabsTrigger>
                <TabsTrigger value="dependencies">
                  <GitBranch className="h-4 w-4 mr-2" />
                  Dependencies
                </TabsTrigger>
              </TabsList>

              <TabsContent value="agents" className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-0.5">
                      <Label>Enable AI Agents</Label>
                      <p className="text-sm text-muted-foreground">Allow AI agents to monitor and update your board</p>
                    </div>
                    <Switch checked={true} />
                  </div>

                  <Separator />

                  <div className="space-y-2">
                    <Label htmlFor="agent-type">Default Agent Type</Label>
                    <Select value={agentType} onValueChange={setAgentType}>
                      <SelectTrigger id="agent-type">
                        <SelectValue placeholder="Select agent type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="task-manager">Task Manager</SelectItem>
                        <SelectItem value="resource-optimizer">Resource Optimizer</SelectItem>
                        <SelectItem value="dependency-resolver">Dependency Resolver</SelectItem>
                        <SelectItem value="progress-tracker">Progress Tracker</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Switch id="auto-assign" checked={autoAssign} onCheckedChange={setAutoAssign} />
                    <Label htmlFor="auto-assign">Auto-assign agents to new tasks</Label>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="resources" className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="resource-limit">Resource Limit (%)</Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="resource-limit"
                      type="number"
                      value={resourceLimit}
                      onChange={(e) => setResourceLimit(Number(e.target.value))}
                      min={10}
                      max={100}
                    />
                    <span className="text-sm text-muted-foreground">{resourceLimit}%</span>
                  </div>
                  <p className="text-sm text-muted-foreground">Maximum resources AI agents can use</p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Resource Metrics to Track</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <div className="flex items-center space-x-2">
                      <Switch id="track-tokens" checked={true} />
                      <Label htmlFor="track-tokens">Token Usage</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="track-cpu" checked={true} />
                      <Label htmlFor="track-cpu">CPU Time</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="track-memory" checked={true} />
                      <Label htmlFor="track-memory">Memory Usage</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="track-api" checked={false} />
                      <Label htmlFor="track-api">API Calls</Label>
                    </div>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="dependencies" className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Auto-detect Dependencies</Label>
                    <p className="text-sm text-muted-foreground">Let AI suggest task dependencies</p>
                  </div>
                  <Switch checked={true} />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label>Dependency Visualization</Label>
                  <Select defaultValue="graph">
                    <SelectTrigger>
                      <SelectValue placeholder="Select visualization type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="graph">Network Graph</SelectItem>
                      <SelectItem value="tree">Tree View</SelectItem>
                      <SelectItem value="matrix">Dependency Matrix</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch id="block-progress" checked={true} />
                  <Label htmlFor="block-progress">Block progress on unresolved dependencies</Label>
                </div>
              </TabsContent>
            </Tabs>

            <DialogFooter>
              <Button variant="outline" className="gap-2">
                <Settings className="h-4 w-4" />
                Advanced Settings
              </Button>
              <Button onClick={handleSave}>Save Changes</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </>
  )
}
