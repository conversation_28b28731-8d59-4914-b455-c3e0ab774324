"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title, DialogFooter } from "@/components/ui/dialog"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface EditColumnDialogProps {
  isOpen: boolean
  onClose: () => void
  column: any
  onSave: (updatedColumn: any) => void
}

export function EditColumnDialog({ isOpen, onClose, column, onSave }: EditColumnDialogProps) {
  const [title, setTitle] = useState(column.title || "")
  const [color, setColor] = useState(column.metadata?.color || "#e2e8f0") // Default color
  const [showColorWheel, setShowColorWheel] = useState(false)

  // Reset state when column changes
  useEffect(() => {
    if (column) {
      setTitle(column.title || "")
      setColor(column.metadata?.color || "#e2e8f0")
    }
  }, [column])

  const handleSave = () => {
    if (!title.trim()) return

    // Create a new column object with the updated values
    const updatedColumn = {
      ...column,
      title: title.trim(), // Ensure we're using the local state title
      metadata: {
        ...column.metadata,
        color: color,
      },
    }

    // Log the updated column for debugging
    console.log("Saving column with title:", title.trim(), "and color:", color)
    console.log("Updated column:", updatedColumn)

    onSave(updatedColumn)
    onClose()
  }

  // Color presets for quick selection
  const colorPresets = [
    "#f87171", // Red
    "#fb923c", // Orange
    "#facc15", // Yellow
    "#4ade80", // Green
    "#60a5fa", // Blue
    "#a78bfa", // Purple
    "#f472b6", // Pink
    "#94a3b8", // Gray
  ]

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Edit Column</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="column-title" className="text-right">
              Name
            </Label>
            <Input
              id="column-title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="col-span-3"
              autoFocus
            />
          </div>
          <div className="grid grid-cols-4 items-start gap-4">
            <Label className="text-right pt-2">Color</Label>
            <div className="col-span-3 space-y-3">
              {/* Color preview */}
              <div
                className="h-10 w-full rounded-md border border-input flex items-center justify-between px-3 cursor-pointer"
                style={{ backgroundColor: color }}
                onClick={() => setShowColorWheel(!showColorWheel)}
              >
                <span className="text-xs font-mono" style={{ color: getContrastColor(color) }}>
                  {color}
                </span>
                <div className="h-6 w-6 rounded-full border border-input" style={{ backgroundColor: color }}></div>
              </div>

              {/* Color wheel */}
              {showColorWheel && (
                <div className="p-2 border rounded-md bg-background">
                  <input
                    type="color"
                    value={color}
                    onChange={(e) => setColor(e.target.value)}
                    className="w-full h-10 cursor-pointer"
                  />
                </div>
              )}

              {/* Color presets */}
              <div className="flex flex-wrap gap-2">
                {colorPresets.map((preset) => (
                  <button
                    key={preset}
                    className="w-6 h-6 rounded-full border border-input transition-transform hover:scale-110 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-1"
                    style={{ backgroundColor: preset }}
                    onClick={() => setColor(preset)}
                    aria-label={`Select color ${preset}`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSave}>Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Helper function to determine text color based on background color
function getContrastColor(hexColor: string) {
  // Convert hex to RGB
  const r = Number.parseInt(hexColor.slice(1, 3), 16)
  const g = Number.parseInt(hexColor.slice(3, 5), 16)
  const b = Number.parseInt(hexColor.slice(5, 7), 16)

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  // Return black or white based on luminance
  return luminance > 0.5 ? "#000000" : "#ffffff"
}
