"use client"
import { CheckIcon, ChevronDownIcon, PlusCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"
import { useBoard } from "./board-context"
import { useRouter } from "next/navigation"
import { CreateBoardDialog } from "./create-board-dialog"

interface BoardSwitcherProps {
  onBoardChange?: (boardId: string) => void
}

export function BoardSwitcher({ onBoardChange }: BoardSwitcherProps = {}) {
  const [showNewBoardDialog, setShowNewBoardDialog] = useState(false)
  const router = useRouter()

  const { boards, activeBoard, setActiveBoard } = useBoard()

  const handleSelectBoard = (boardId: string) => {
    if (!activeBoard || activeBoard.id !== boardId) {
      setActiveBoard(boardId)
      // Only call onBoardChange if it's a function
      if (typeof onBoardChange === "function") {
        onBoardChange(boardId)
      }
    }

    // Navigate to the selected board
    router.push(`/?board=${boardId}`)
  }

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="outline" className="w-[240px] justify-between text-sm font-normal">
            {activeBoard?.name || "Select Board"}
            <ChevronDownIcon className="ml-auto h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-[240px]">
          <DropdownMenuLabel>Boards</DropdownMenuLabel>
          <DropdownMenuSeparator />
          {boards.map((board) => (
            <DropdownMenuItem key={board.id} onClick={() => handleSelectBoard(board.id)} className="cursor-pointer">
              {board.name}
              {activeBoard && activeBoard.id === board.id && <CheckIcon className="ml-auto h-4 w-4" />}
            </DropdownMenuItem>
          ))}
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={() => setShowNewBoardDialog(true)} className="cursor-pointer">
            <PlusCircle className="mr-2 h-4 w-4" />
            Create Board
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      <CreateBoardDialog open={showNewBoardDialog} onOpenChange={setShowNewBoardDialog} />
    </>
  )
}
