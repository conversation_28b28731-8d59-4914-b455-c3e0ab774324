"use client"

import { useEffect, useRef, useState } from "react"
import { Card } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { ZoomIn, ZoomOut, RefreshCw } from "lucide-react"
import { useBoard } from "./board-context"

interface Node {
  id: string
  label: string
  color: string
}

interface Edge {
  from: string
  to: string
}

interface DependencyGraphProps {
  cardId?: string
  showAllCards?: boolean
}

export function DependencyGraph({ cardId, showAllCards = false }: DependencyGraphProps) {
  const graphRef = useRef<HTMLDivElement>(null)
  const [selectedCard, setSelectedCard] = useState<string | undefined>(cardId)
  const [zoom, setZoom] = useState(1)
  const boardContext = useBoard()

  // Mock data for demonstration
  const nodes: Node[] = [
    { id: "card-1", label: "Setup Database", color: "#4CAF50" },
    { id: "card-2", label: "Create API Endpoints", color: "#2196F3" },
    { id: "card-3", label: "Implement Authentication", color: "#FFC107" },
    { id: "card-4", label: "Design UI Components", color: "#9C27B0" },
    { id: "card-5", label: "Integrate Frontend with API", color: "#F44336" },
    { id: "card-6", label: "Write Tests", color: "#607D8B" },
    { id: "card-7", label: "Documentation", color: "#795548" },
  ]

  const edges: Edge[] = [
    { from: "card-1", to: "card-2" },
    { from: "card-2", to: "card-3" },
    { from: "card-2", to: "card-5" },
    { from: "card-3", to: "card-5" },
    { from: "card-4", to: "card-5" },
    { from: "card-5", to: "card-6" },
    { from: "card-6", to: "card-7" },
  ]

  // Initialize the graph
  useEffect(() => {
    if (!graphRef.current) return

    // In a real implementation, this would use a library like vis.js or d3.js
    // to render the graph. For now, we'll just render a placeholder.
    const renderGraph = () => {
      const graph = graphRef.current
      if (!graph) return

      // Clear previous content
      graph.innerHTML = ""

      // Create a simple SVG representation
      const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg")
      svg.setAttribute("width", "100%")
      svg.setAttribute("height", "300")
      svg.setAttribute("viewBox", "0 0 800 300")
      svg.style.transform = `scale(${zoom})`
      svg.style.transformOrigin = "center"
      svg.style.transition = "transform 0.3s ease"

      // Draw edges first (so they're behind nodes)
      edges.forEach((edge) => {
        const fromNode = nodes.find((n) => n.id === edge.from)
        const toNode = nodes.find((n) => n.id === edge.to)
        if (!fromNode || !toNode) return

        // Calculate positions (simplified for this example)
        const fromIndex = nodes.findIndex((n) => n.id === edge.from)
        const toIndex = nodes.findIndex((n) => n.id === edge.to)
        const fromX = 100 + fromIndex * 100
        const fromY = 150
        const toX = 100 + toIndex * 100
        const toY = 150

        // Create line
        const line = document.createElementNS("http://www.w3.org/2000/svg", "line")
        line.setAttribute("x1", fromX.toString())
        line.setAttribute("y1", fromY.toString())
        line.setAttribute("x2", toX.toString())
        line.setAttribute("y2", toY.toString())
        line.setAttribute("stroke", "#999")
        line.setAttribute("stroke-width", "2")
        line.setAttribute("marker-end", "url(#arrowhead)")

        svg.appendChild(line)
      })

      // Create arrowhead marker
      const defs = document.createElementNS("http://www.w3.org/2000/svg", "defs")
      const marker = document.createElementNS("http://www.w3.org/2000/svg", "marker")
      marker.setAttribute("id", "arrowhead")
      marker.setAttribute("markerWidth", "10")
      marker.setAttribute("markerHeight", "7")
      marker.setAttribute("refX", "9")
      marker.setAttribute("refY", "3.5")
      marker.setAttribute("orient", "auto")

      const polygon = document.createElementNS("http://www.w3.org/2000/svg", "polygon")
      polygon.setAttribute("points", "0 0, 10 3.5, 0 7")
      polygon.setAttribute("fill", "#999")

      marker.appendChild(polygon)
      defs.appendChild(marker)
      svg.appendChild(defs)

      // Draw nodes
      nodes.forEach((node, index) => {
        // Calculate position (simplified for this example)
        const x = 100 + index * 100
        const y = 150

        // Create circle for node
        const circle = document.createElementNS("http://www.w3.org/2000/svg", "circle")
        circle.setAttribute("cx", x.toString())
        circle.setAttribute("cy", y.toString())
        circle.setAttribute("r", "30")
        circle.setAttribute("fill", node.color)
        circle.setAttribute("stroke", "#fff")
        circle.setAttribute("stroke-width", "2")

        // Highlight selected node
        if (node.id === selectedCard) {
          circle.setAttribute("stroke", "#000")
          circle.setAttribute("stroke-width", "3")
        }

        // Create text label
        const text = document.createElementNS("http://www.w3.org/2000/svg", "text")
        text.setAttribute("x", x.toString())
        text.setAttribute("y", (y + 50).toString())
        text.setAttribute("text-anchor", "middle")
        text.setAttribute("fill", "#333")
        text.setAttribute("font-size", "12")
        text.textContent = node.label

        svg.appendChild(circle)
        svg.appendChild(text)
      })

      graph.appendChild(svg)
    }

    renderGraph()
  }, [selectedCard, zoom, nodes, edges])

  // Handle zoom in
  const handleZoomIn = () => {
    setZoom((prev) => Math.min(prev + 0.2, 2))
  }

  // Handle zoom out
  const handleZoomOut = () => {
    setZoom((prev) => Math.max(prev - 0.2, 0.5))
  }

  // Handle reset zoom
  const handleResetZoom = () => {
    setZoom(1)
  }

  return (
    <Card className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h3 className="text-lg font-medium">Dependency Graph</h3>
        <div className="flex space-x-2">
          <Button variant="outline" size="icon" onClick={handleZoomIn}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={handleZoomOut}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon" onClick={handleResetZoom}>
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="mb-4">
        <Select value={selectedCard} onValueChange={setSelectedCard}>
          <SelectTrigger>
            <SelectValue placeholder="Select a card" />
          </SelectTrigger>
          <SelectContent>
            {nodes.map((node) => (
              <SelectItem key={node.id} value={node.id}>
                {node.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div ref={graphRef} className="h-[300px] border rounded-md overflow-hidden bg-muted/20" />

      <div className="mt-4 text-sm text-muted-foreground">
        <p>Showing dependencies for selected card. Zoom or select a different card to explore.</p>
      </div>
    </Card>
  )
}
