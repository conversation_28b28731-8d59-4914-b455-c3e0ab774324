"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { X, Minimize, Maximize2, ArrowDownToLine } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface FloatingPanelProps {
  id: string
  title: string
  isOpen: boolean
  onClose: () => void
  onDock: () => void
  children: React.ReactNode
  initialPosition?: { x: number; y: number }
  initialSize?: { width: number; height: number }
  minWidth?: number
  minHeight?: number
}

export default function FloatingPanel({
  id,
  title,
  isOpen,
  onClose,
  onDock,
  children,
  initialPosition = { x: 100, y: 100 },
  initialSize = { width: 600, height: 400 },
  minWidth = 300,
  minHeight = 200,
}: FloatingPanelProps) {
  const [position, setPosition] = useState(initialPosition)
  const [size, setSize] = useState(initialSize)
  const [isDragging, setIsDragging] = useState(false)
  const [isResizing, setIsResizing] = useState(false)
  const [resizeDirection, setResizeDirection] = useState<string | null>(null)
  const [isMaximized, setIsMaximized] = useState(false)
  const [preMaximizeState, setPreMaximizeState] = useState({
    position: initialPosition,
    size: initialSize,
  })

  const panelRef = useRef<HTMLDivElement>(null)
  const dragStartRef = useRef({ x: 0, y: 0 })
  const resizeStartRef = useRef({ width: 0, height: 0, x: 0, y: 0 })

  // Handle dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (isMaximized) return

    // Prevent dragging when clicking on buttons or interactive elements
    if ((e.target as HTMLElement).closest("button, input, select, a")) {
      return
    }

    setIsDragging(true)
    dragStartRef.current = {
      x: e.clientX - position.x,
      y: e.clientY - position.y,
    }
  }

  // Handle resizing
  const handleResizeMouseDown = (e: React.MouseEvent, direction: string) => {
    e.stopPropagation()
    if (isMaximized) return

    setIsResizing(true)
    setResizeDirection(direction)
    resizeStartRef.current = {
      width: size.width,
      height: size.height,
      x: e.clientX,
      y: e.clientY,
    }
  }

  // Toggle maximize state
  const toggleMaximize = () => {
    if (isMaximized) {
      setIsMaximized(false)
      setPosition(preMaximizeState.position)
      setSize(preMaximizeState.size)
    } else {
      setPreMaximizeState({ position, size })
      setIsMaximized(true)
      setPosition({ x: 0, y: 0 })
      setSize({ width: window.innerWidth, height: window.innerHeight })
    }
  }

  // Handle mouse move for both dragging and resizing
  useEffect(() => {
    const handleMouseMove = (e: MouseEvent) => {
      if (isDragging) {
        const newX = e.clientX - dragStartRef.current.x
        const newY = e.clientY - dragStartRef.current.y

        // Keep panel within viewport
        const maxX = window.innerWidth - size.width
        const maxY = window.innerHeight - size.height

        setPosition({
          x: Math.max(0, Math.min(newX, maxX)),
          y: Math.max(0, Math.min(newY, maxY)),
        })
      } else if (isResizing && resizeDirection) {
        e.preventDefault()

        const deltaX = e.clientX - resizeStartRef.current.x
        const deltaY = e.clientY - resizeStartRef.current.y

        let newWidth = resizeStartRef.current.width
        let newHeight = resizeStartRef.current.height
        let newX = position.x
        let newY = position.y

        // Handle different resize directions
        if (resizeDirection.includes("e")) {
          newWidth = Math.max(minWidth, resizeStartRef.current.width + deltaX)
        }
        if (resizeDirection.includes("s")) {
          newHeight = Math.max(minHeight, resizeStartRef.current.height + deltaY)
        }
        if (resizeDirection.includes("w")) {
          const widthChange = resizeStartRef.current.x - e.clientX
          newWidth = Math.max(minWidth, resizeStartRef.current.width + widthChange)
          newX = position.x - (newWidth - resizeStartRef.current.width)
        }
        if (resizeDirection.includes("n")) {
          const heightChange = resizeStartRef.current.y - e.clientY
          newHeight = Math.max(minHeight, resizeStartRef.current.height + heightChange)
          newY = position.y - (newHeight - resizeStartRef.current.height)
        }

        setSize({ width: newWidth, height: newHeight })
        setPosition({ x: newX, y: newY })
      }
    }

    const handleMouseUp = () => {
      setIsDragging(false)
      setIsResizing(false)
      setResizeDirection(null)
    }

    if (isDragging || isResizing) {
      document.addEventListener("mousemove", handleMouseMove)
      document.addEventListener("mouseup", handleMouseUp)
    }

    return () => {
      document.removeEventListener("mousemove", handleMouseMove)
      document.removeEventListener("mouseup", handleMouseUp)
    }
  }, [isDragging, isResizing, position, size, resizeDirection, minWidth, minHeight])

  if (!isOpen) return null

  return (
    <div
      ref={panelRef}
      className={cn(
        "fixed bg-background border border-editor-border rounded-md shadow-lg overflow-hidden z-50 flex flex-col",
        isDragging && "cursor-grabbing",
        isMaximized && "top-0 left-0 right-0 bottom-0 w-screen h-screen",
      )}
      style={
        isMaximized
          ? { width: "100vw", height: "100vh" }
          : {
              width: `${size.width}px`,
              height: `${size.height}px`,
              left: `${position.x}px`,
              top: `${position.y}px`,
            }
      }
    >
      {/* Header/Title bar */}
      <div
        className="h-9 bg-editor-sidebar-bg border-b border-editor-border flex items-center justify-between px-3 cursor-grab"
        onMouseDown={handleMouseDown}
      >
        <div className="text-sm font-medium truncate">{title}</div>
        <div className="flex items-center space-x-1">
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-muted-foreground hover:text-foreground"
            onClick={toggleMaximize}
          >
            {isMaximized ? <Minimize className="h-3.5 w-3.5" /> : <Maximize2 className="h-3.5 w-3.5" />}
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-muted-foreground hover:text-foreground"
            onClick={onDock}
            title="Dock to main window"
          >
            <ArrowDownToLine className="h-3.5 w-3.5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="h-6 w-6 text-muted-foreground hover:text-foreground"
            onClick={onClose}
          >
            <X className="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-hidden">{children}</div>

      {/* Resize handles */}
      {!isMaximized && (
        <>
          <div
            className="absolute top-0 left-0 w-3 h-3 cursor-nwse-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "nw")}
          />
          <div
            className="absolute top-0 right-0 w-3 h-3 cursor-nesw-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "ne")}
          />
          <div
            className="absolute bottom-0 left-0 w-3 h-3 cursor-nesw-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "sw")}
          />
          <div
            className="absolute bottom-0 right-0 w-3 h-3 cursor-nwse-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "se")}
          />
          <div
            className="absolute top-0 left-3 right-3 h-1 cursor-ns-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "n")}
          />
          <div
            className="absolute bottom-0 left-3 right-3 h-1 cursor-ns-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "s")}
          />
          <div
            className="absolute left-0 top-3 bottom-3 w-1 cursor-ew-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "w")}
          />
          <div
            className="absolute right-0 top-3 bottom-3 w-1 cursor-ew-resize"
            onMouseDown={(e) => handleResizeMouseDown(e, "e")}
          />
        </>
      )}
    </div>
  )
}
