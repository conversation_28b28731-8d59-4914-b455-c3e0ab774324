"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, ArrowLeft, Cpu } from "lucide-react" // Changed Robot to Cpu
import { useSearch } from "./search-provider"
import { useBoard } from "./board-context"
import { useAgentBoardController } from "./agent-board-controller"

interface HeaderProps {
  showAgentPanel: boolean
  toggleAgentPanel: () => void
  onExitKanban: () => void
}

export function Header({ showAgentPanel, toggleAgentPanel, onExitKanban }: HeaderProps) {
  const { searchQuery, setSearchQuery } = useSearch()
  const { boards, activeBoard, setActiveBoard } = useBoard()
  const { isAgentRunning, startAgent, stopAgent } = useAgentBoardController()
  const [isSearchFocused, setIsSearchFocused] = useState(false)

  return (
    <header className="border-b border-border h-14 px-4 flex items-center justify-between bg-background">
      <div className="flex items-center gap-4">
        <Button variant="ghost" onClick={onExitKanban} className="flex items-center gap-2 text-primary hover:text-primary/80">
          <ArrowLeft className="h-4 w-4" />
          <span>Back to Explorer</span>
        </Button>

        <div className="flex items-center gap-2">
          <h1 className="text-lg font-semibold">{activeBoard?.name || "Kanban Board"}</h1>
          {activeBoard && boards.length > 1 && (
            <select
              className="bg-background border border-input rounded px-2 py-1 text-sm h-9"
              value={activeBoard.id}
              onChange={(e) => setActiveBoard(e.target.value)}
            >
              {boards.map((board) => (
                <option key={board.id} value={board.id}>
                  {board.name}
                </option>
              ))}
            </select>
          )}
        </div>
      </div>

      <div className="flex items-center gap-2">
        <div
          className={`relative transition-all ${
            isSearchFocused ? "w-80" : "w-64"
          }`}
        >
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search cards..."
            className="pl-8 h-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            onFocus={() => setIsSearchFocused(true)}
            onBlur={() => setIsSearchFocused(false)}
          />
        </div>

        <Button variant="outline" size="sm" onClick={toggleAgentPanel} className="h-9">
          <Cpu className={`h-4 w-4 mr-2 ${showAgentPanel ? "text-primary" : ""}`} />
          Agent
        </Button>

        <Button
          variant={isAgentRunning ? "destructive" : "default"}
          size="sm"
          onClick={isAgentRunning ? stopAgent : startAgent}
          className="h-9"
        >
          {isAgentRunning ? "Stop Agent" : "Start Agent"}
        </Button>
      </div>
    </header>
  )
}